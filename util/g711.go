package util

import (
	"encoding/binary"
	"github.com/zaf/g711"
)

// PCMToG711PCMU converts 16bit LPCM data to G711 u-law data.
func PCMToG711PCMU(pcm []int16) ([]byte, error) {
	pcmu := make([]byte, len(pcm))
	for i, v := range pcm {
		pcmu[i] = g711.EncodeUlawFrame(v)
	}

	return pcmu, nil
}

// PCMToG711PCMA converts 16bit LPCM data to G711 A-law data.
func PCMToG711PCMA(pcm []int16) ([]byte, error) {
	pcma := make([]byte, len(pcm))
	for i, v := range pcm {
		pcma[i] = g711.EncodeAlawFrame(v)
	}

	return pcma, nil
}

// G711PCMAToPCM converts G711 A-law data to 16bit LPCM data.
func G711PCMAToPCM(pcma []byte) ([]int16, error) {
	pcm := make([]int16, len(pcma))
	for i, v := range pcma {
		pcm[i] = g711.DecodeAlawFrame(v)
	}

	return pcm, nil
}

// G711PCMUToPCM converts G711 u-law data to 16bit LPCM data.
func G711PCMUToPCM(pcmu []byte) ([]int16, error) {
	pcm := make([]int16, len(pcmu))
	for i, v := range pcmu {
		pcm[i] = g711.DecodeUlawFrame(v)
	}

	return pcm, nil
}

func BytesToInt16Slice(input []byte) []int16 {
	if len(input)%2 != 0 {
		panic("input slice length must be even")
	}
	// Create a slice header for the output int16 slice.
	int16Slice := make([]int16, len(input)/2)
	// Copy the bytes to the int16 slice.
	for i := 0; i < len(input); i += 2 {
		int16Slice[i/2] = int16(binary.LittleEndian.Uint16(input[i : i+2]))
	}
	return int16Slice
}
