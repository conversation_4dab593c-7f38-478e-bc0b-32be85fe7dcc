package util

import (
	"fmt"
	"net"
)

func resolveStringIP(input string) (net.IP, error) {
	// 1. Try parsing as a literal IP
	ip := net.ParseIP(input)
	if ip != nil {
		return ip, nil // It's a valid IP
	}

	// 2. Resolve as a DNS name
	ips, err := net.LookupIP(input)
	if err != nil {
		return nil, fmt.Errorf("failed to resolve as IP or DNS: %w", err)
	}

	if len(ips) == 0 {
		return nil, fmt.Errorf("no ip address found")
	}

	return ips[0], nil // Return the first IP address found (You can implement a different selection logic if needed)
}

func GetLocalIPByRemoteHost(remoteHostOrIP string) (net.IP, error) {
	rIP, err := resolveStringIP(remoteHostOrIP)
	if err != nil {
		return nil, err
	}

	conn, err := net.DialUDP("udp", nil, &net.UDPAddr{IP: rIP, Port: 53})
	if err != nil {
		return nil, err
	}
	defer conn.Close()

	localAddr := conn.LocalAddr().(*net.UDPAddr)
	return localAddr.IP, nil
}

func GetLocalFreePort() (int, error) {
	listener, err := net.Listen("tcp", "localhost:0")
	if err != nil {
		return 0, fmt.Errorf("failed to get a free port: %w", err)
	}
	defer listener.Close()

	addr := listener.Addr().(*net.TCPAddr)
	return addr.Port, nil
}
