# Flutter 应用图标配置说明

本文档说明如何使用 `flutter_launcher_icons` 包为 Flutter 应用生成多平台图标。

## 配置文件

图标配置已添加到 `pubspec.yaml` 文件中：

```yaml
flutter_launcher_icons:
  android: true
  ios: true
  image_path: "assert/images/bfdx-icon.png"
  min_sdk_android: 21
  remove_alpha_ios: true
  web:
    generate: true
    image_path: "assert/images/bfdx-icon.png"
    background_color: "#FFFFFF"
    theme_color: "#4285F4"
  windows:
    generate: true
    image_path: "assert/images/bfdx-icon.png"
    icon_size: 48
  macos:
    generate: true
    image_path: "assert/images/bfdx-icon.png"
```

## 图标要求

- **源图标文件**: `assert/images/bfdx-icon.png`
- **推荐尺寸**: 1024x1024 像素或更大
- **格式**: PNG 格式
- **注意**: iOS 平台不支持透明通道，工具会自动移除 alpha 通道

## 使用方法

### 方法一：使用脚本（推荐）

运行项目根目录下的脚本：

```bash
./scripts/generate_flutter_icons.sh
```

该脚本会：
- 检查 Flutter 环境
- 验证图标文件存在
- 获取项目依赖
- 生成所有平台图标
- 执行 Linux 平台特殊设置
- 验证生成结果

### 方法二：手动运行

在 `flutter_client` 目录下执行：

```bash
flutter pub get
dart run flutter_launcher_icons
```

## 生成的图标文件

### Android
- `android/app/src/main/res/mipmap-*/ic_launcher.png`
- 包含多种分辨率：mdpi, hdpi, xhdpi, xxhdpi, xxxhdpi

### iOS
- `ios/Runner/Assets.xcassets/AppIcon.appiconset/`
- 包含所有 iOS 要求的图标尺寸和 Contents.json

### Web
- `web/icons/Icon-192.png`
- `web/icons/Icon-512.png`
- `web/icons/Icon-maskable-192.png`
- `web/icons/Icon-maskable-512.png`
- `web/favicon.png`

### macOS
- `macos/Runner/Assets.xcassets/AppIcon.appiconset/`
- 包含 macOS 应用所需的各种尺寸

### Windows
- `windows/runner/resources/app_icon.ico`

### Linux
- `linux/icons/app_icon.png`
- 由脚本额外复制的图标文件

## 重新生成图标

如果需要更换图标：

1. 替换 `assert/images/bfdx-icon.png` 文件
2. 运行生成脚本：`./scripts/generate_flutter_icons.sh`

## 故障排除

### 常见问题

1. **图标文件不存在**
   - 确保 `assert/images/bfdx-icon.png` 文件存在
   - 检查文件路径是否正确

2. **权限问题**
   - 确保脚本有执行权限：`chmod +x scripts/generate_flutter_icons.sh`

3. **Flutter 环境问题**
   - 确保 Flutter SDK 已正确安装
   - 运行 `flutter doctor` 检查环境

4. **依赖问题**
   - 运行 `flutter pub get` 获取最新依赖
   - 检查 `pubspec.yaml` 中是否包含 `flutter_launcher_icons: ^0.14.4`

### 验证生成结果

脚本会自动验证生成的图标文件，如果验证失败，请检查：
- 源图标文件是否有效
- 各平台目录结构是否正确
- 是否有文件权限问题

## 高级配置

如需更复杂的配置（如自适应图标、通知图标等），可以参考 [flutter_launcher_icons 官方文档](https://pub.dev/packages/flutter_launcher_icons)。

## 支持的平台

- ✅ Android
- ✅ iOS
- ✅ Web
- ✅ macOS
- ✅ Windows
- ✅ Linux
