package codec

import (
	"bytes"
	"encoding/binary"
	"fmt"
	"io"
	"os"

	"github.com/go-audio/wav"
	"github.com/hajimehoshi/go-mp3"
	"github.com/zeozeozeo/gomplerate"
)

// Mp3FileToPcm8k16bit pcm is 8k16bit little endian mono
func Mp3FileToPcm8k16bit(mp3FilePath string) ([]int16, error) {
	file, err := os.Open(mp3FilePath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	decoder, err := mp3.NewDecoder(file)
	if err != nil {
		return nil, err
	}

	sourceSampleRate := decoder.SampleRate()
	targetSampleRate := 8000

	pcmData, err := io.ReadAll(decoder)
	if err != nil {
		return nil, err
	}

	// The decoded stream is 16-bit little-endian stereo PCM.
	// We need to convert it to mono.
	numSamples := len(pcmData) / 4 // 2 channels * 2 bytes/sample
	stereoPcm := make([]int16, numSamples*2)
	if err := binary.Read(bytes.NewReader(pcmData), binary.LittleEndian, &stereoPcm); err != nil {
		return nil, err
	}

	monoPcm := make([]int16, numSamples)
	for i := 0; i < numSamples; i++ {
		monoPcm[i] = int16((int32(stereoPcm[i*2]) + int32(stereoPcm[i*2+1])) / 2)
	}

	if sourceSampleRate == 8000 {
		return monoPcm, nil
	}

	resampler, err := gomplerate.NewResampler(1, sourceSampleRate, targetSampleRate)
	if err != nil {
		return nil, err
	}

	resamplePcm := resampler.ResampleInt16(monoPcm)

	return resamplePcm, nil
}

// WavFileToPcm8k16bit pcm is 8k16bit little endian mono
func WavFileToPcm8k16bit(wavFilePath string) ([]int16, error) {
	file, err := os.Open(wavFilePath)
	if err != nil {
		return nil, err
	}
	defer file.Close()

	decoder := wav.NewDecoder(file)
	if !decoder.IsValidFile() {
		return nil, fmt.Errorf("invalid wav file")
	}

	buffer, err := decoder.FullPCMBuffer()
	if err != nil {
		return nil, err
	}

	sourceSampleRate := buffer.Format.SampleRate
	numChannels := buffer.Format.NumChannels
	targetSampleRate := 8000

	// The go-audio library decodes audio into a slice of ints.
	// We need to convert it to int16, considering the source bit depth.
	pcmInt16 := make([]int16, len(buffer.Data))
	switch buffer.SourceBitDepth {
	case 8:
		// 8-bit audio is unsigned (0-255), convert to signed 16-bit (-32768 to 32767)
		for i, s := range buffer.Data {
			pcmInt16[i] = int16((s - 128) * 256)
		}
	case 16:
		// 16-bit audio, direct conversion
		for i, s := range buffer.Data {
			pcmInt16[i] = int16(s)
		}
	case 24:
		// 24-bit audio, scale down to 16-bit
		for i, s := range buffer.Data {
			pcmInt16[i] = int16(s >> 8)
		}
	case 32:
		// 32-bit audio, scale down to 16-bit
		for i, s := range buffer.Data {
			pcmInt16[i] = int16(s >> 16)
		}
	default:
		return nil, fmt.Errorf("unsupported bit depth: %d", buffer.SourceBitDepth)
	}

	var monoPcm []int16
	// Convert to mono if stereo
	if numChannels == 2 {
		monoPcm = make([]int16, len(pcmInt16)/2)
		for i := 0; i < len(monoPcm); i++ {
			// Average the two channels
			monoPcm[i] = int16((int32(pcmInt16[i*2]) + int32(pcmInt16[i*2+1])) / 2)
		}
	} else if numChannels == 1 {
		monoPcm = pcmInt16
	} else {
		return nil, fmt.Errorf("unsupported number of channels: %d", numChannels)
	}

	// Resample if necessary
	if sourceSampleRate == targetSampleRate {
		return monoPcm, nil
	}

	resampler, err := gomplerate.NewResampler(1, sourceSampleRate, targetSampleRate)
	if err != nil {
		return nil, err
	}

	resampledPcm := resampler.ResampleInt16(monoPcm)

	return resampledPcm, nil
}
