package main

import (
	"reflect"
	"testing"
)

func Test_slice_index(t *testing.T) {
	testCases := []struct {
		name      string
		pcmInput  []int16
		startIdx  int
		expectLen int
		expectPad int
	}{
		{
			name:      "exactly 480 samples",
			pcmInput:  fillInt16(480, 1),
			startIdx:  0,
			expectLen: 480,
			expectPad: 0,
		},
		{
			name:      "less than 480 samples",
			pcmInput:  fillInt16(300, 1),
			startIdx:  0,
			expectLen: 480,
			expectPad: 180,
		},
		{
			name:      "more than 480 samples, start at 600",
			pcmInput:  fillInt16(1000, 1),
			startIdx:  600,
			expectLen: 400,
			expectPad: 80,
		},
		{
			name:      "start at 0, input 1000",
			pcmInput:  fillInt16(1000, 1),
			startIdx:  0,
			expectLen: 480,
			expectPad: 0,
		},
		{
			name:      "start at 480, input 1000",
			pcmInput:  fillInt16(1000, 1),
			startIdx:  480,
			expectLen: 480,
			expectPad: 0,
		},
		{
			name:      "start at 950, input 1000",
			pcmInput:  fillInt16(1000, 1),
			startIdx:  950,
			expectLen: 50,
			expectPad: 430,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			var pcm60ms []int16
			i := tc.startIdx
			if i+480 > len(tc.pcmInput) {
				pcm60ms = append(tc.pcmInput[i:], make([]int16, 480-len(tc.pcmInput[i:]))...)
			} else {
				pcm60ms = tc.pcmInput[i : i+480]
			}
			if len(pcm60ms) != 480 {
				t.Errorf("expected length 480, got %d", len(pcm60ms))
			}
			padCount := 0
			for _, v := range pcm60ms {
				if v == 0 {
					padCount++
				}
			}
			if padCount != tc.expectPad {
				t.Errorf("expected pad count %d, got %d", tc.expectPad, padCount)
			}
			if tc.expectPad == 0 && !reflect.DeepEqual(pcm60ms, tc.pcmInput[i:i+480]) {
				t.Errorf("expected slice to match input")
			}
		})
	}
}

// fillInt16 returns a slice of int16 of given length, filled with val
func fillInt16(n int, val int16) []int16 {
	s := make([]int16, n)
	for i := range s {
		s[i] = val
	}
	return s
}
