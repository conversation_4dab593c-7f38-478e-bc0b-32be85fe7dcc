package main

import (
	"flag"
	"fmt"
	"os"
	"sipserverclient"
	"sipserverclient/codec"
	"sipserverclient/sipclient"
	"strconv"
	"strings"
	"sync"
	"time"
)

func main() {
	var (
		showHelp      bool
		showVersion   bool
		serverAddr    string
		serverPort    int
		username      string
		password      string
		sipTransport  string
		dmrTarget     string
		soundFilePath string
	)

	flag.BoolVar(&showHelp, "h", false, "Show help message")
	flag.BoolVar(&showHelp, "help", false, "Show help message")
	flag.BoolVar(&showVersion, "v", false, "Show version information")
	flag.BoolVar(&showVersion, "version", false, "Show version information")

	flag.StringVar(&serverAddr, "s", "", "Set the sipserver server address, required")
	flag.StringVar(&serverAddr, "server", "", "Set the sipserver server address, required")

	flag.IntVar(&serverPort, "p", 0, "Set the sipserver server port, required")
	flag.IntVar(&serverPort, "port", 0, "Set the sipserver server port, required")

	flag.StringVar(&username, "u", "", "Set the sipserver server username, required")
	flag.StringVar(&username, "user", "", "Set the sipserver server username, required")

	flag.StringVar(&password, "P", "", "Set the sipserver server password, required")
	flag.StringVar(&password, "password", "", "Set the sipserver server password, required")

	flag.StringVar(
		&sipTransport,
		"transport",
		"udp",
		"Set SIP connection protocol, default is udp, value can be tcp or udp",
	)
	flag.StringVar(
		&sipTransport,
		"sip-transport",
		"udp",
		"Set SIP connection protocol, default is udp, value can be tcp or udp",
	)

	flag.StringVar(&dmrTarget, "dmr-target", "", "Set the DMR call target, required")
	flag.StringVar(
		&soundFilePath,
		"sound",
		"",
		"Set the path of the audio file to play, supports wav, mp3 format, required",
	)

	flag.Usage = func() {
		_, _ = fmt.Fprintf(os.Stderr, "Usage of %s:\n", os.Args[0])
		flag.PrintDefaults()
	}

	flag.Parse()

	if showHelp {
		flag.Usage()
		os.Exit(0)
	}

	if showVersion {
		fmt.Println(sipserverclient.Version)
		os.Exit(0)
	}

	// Check for required flags
	if serverAddr == "" || serverPort == 0 || username == "" || password == "" || dmrTarget == "" ||
		soundFilePath == "" {
		fmt.Println("1 Error: Missing required arguments. Use -h or --help for usage.")
		flag.Usage()
		os.Exit(1)
	}

	if sipTransport != "udp" && sipTransport != "tcp" {
		fmt.Println("1 Error: -transport/--sip-transport value must be 'tcp' or 'udp'")
		flag.Usage()
		os.Exit(1)
	}

	if strings.HasPrefix(dmrTarget, "0x") || strings.HasPrefix(dmrTarget, "0X") {
		dmrTarget = dmrTarget[2:] // Remove the '0x' prefix
		targetId, err := strconv.ParseUint(dmrTarget, 16, 32)
		if err != nil {
			fmt.Printf("1 Error: Invalid DMR Call Target: %s\n", dmrTarget)
			flag.Usage()
			os.Exit(1)
		}
		dmrTarget = strconv.FormatUint(targetId, 10)
	}

	fmt.Println("Arguments parsed successfully:")
	fmt.Printf("  - Server Address: %s\n", serverAddr)
	fmt.Printf("  - Server Port: %d\n", serverPort)
	fmt.Printf("  - Username: %s\n", username)
	fmt.Printf("  - Password: %s\n", "******")
	fmt.Printf("  - SIP Transport: %s\n", sipTransport)
	fmt.Printf("  - DMR Call Target: %s\n", dmrTarget)
	fmt.Printf("  - Sound File: %s\n", soundFilePath)

	var audioPcm []int16
	var audioPcmWg sync.WaitGroup
	audioPcmWg.Add(1)
	go func() {
		//if end with wav
		if strings.ToLower(soundFilePath[len(soundFilePath)-4:]) == ".wav" {
			pcm, err := codec.WavFileToPcm8k16bit(soundFilePath)
			if err != nil {
				fmt.Printf("4 Failed to convert wav file: %v\n", err)
				os.Exit(4)
			} else {
				audioPcm = pcm
			}
		}
		//if end with mp3
		if strings.ToLower(soundFilePath[len(soundFilePath)-4:]) == ".mp3" {
			pcm, err := codec.Mp3FileToPcm8k16bit(soundFilePath)
			if err != nil {
				fmt.Printf("4 Failed to play mp3 file: %v\n", err)
				os.Exit(4)
			} else {
				audioPcm = pcm
			}
		}

		audioPcmWg.Done()
	}()

	client, err := sipclient.NewSipClient(
		sipTransport,
		serverAddr,
		serverPort,
		username,
		password,
	)
	if err != nil {
		fmt.Printf("2 Failed to create SIP client: %v\n", err)
		os.Exit(2)
	}
	defer client.Close()

	err = client.RegisterAndKeepAlive()
	if err != nil {
		fmt.Printf("3 Failed to register with SIP server: %v\n", err)
		os.Exit(3)
	}

	audioPcmWg.Wait()

	if len(audioPcm) == 0 {
		fmt.Printf("4 convert sound to pcm error,pcm len == 0\n")
		os.Exit(4)
	}

	err = client.ProcessCall(dmrTarget)
	if err != nil {
		return
	}

	dialog := client.Dialog.Load()
	if dialog == nil {
		fmt.Printf("5 Failed to process call: \n")
		os.Exit(5)
	}

	for i := 0; i < len(audioPcm); i += 480 {
		//60ms pcm
		var pcm60ms []int16
		if i+480 > len(audioPcm) {
			pcm60ms = append(
				audioPcm[i:],
				make([]int16, 480-len(audioPcm[i:]))...) // Pad with silence if less than 480 samples
		} else {
			pcm60ms = audioPcm[i : i+480]
		}
		dialog.GotOutputPcm(pcm60ms)
		if dialog.Status.Load() >= sipclient.DialogStatusCallend {
			return
		}

		time.Sleep(60*time.Millisecond - 1)
	}

	if dialog.Status.Load() < sipclient.DialogStatusCallend {
		dialog.EndCall()
	}

	err = client.UnRegister()
	if err != nil {
		fmt.Printf("6 Failed to unregister with SIP server: %v\n", err)
		os.Exit(6)
	}
}
