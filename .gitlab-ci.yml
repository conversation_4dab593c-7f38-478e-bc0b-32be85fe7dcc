variables:
  BuildDir: ./build

.go-cache:
  variables:
    GOMODCACHE: $CI_PROJECT_DIR/.gomodcache
  before_script:
    - export GOPROXY='https://goproxy.cn|https://goproxy.io,direct'
    - export GOSUMDB=off
    - export GOPRIVATE=git.kicad99.com
    - mkdir -p $GOMODCACHE
  cache:
    key:
      files:
        - go.sum
      prefix: ${CI_COMMIT_REF_SLUG}
    paths:
      - $GOMODCACHE

stages:
  - lint-go
  - build-linux64-cli
  - build-win64-cli

lint-go:
  stage: lint-go
  image: ghcr.io/yangjuncode/go:1.24.3
  tags:
    - docker
  extends:
    - .go-cache
  rules:
    - if: '$CI_COMMIT_REF_NAME == "main"'
  script:
    - go vet ./...

build-linux64-cli:
  stage: build-linux64-cli
  image: ghcr.io/yangjuncode/go:1.24.3
  tags:
    - docker
  extends:
    - .go-cache
  rules:
    - if: '$CI_COMMIT_REF_NAME == "main"'
      changes:
        - version.txt
  script:
    - ./scripts/build_linux_cli.sh
  artifacts:
    expire_in: 7 day
    name: sip_server_client_cli_linux_x64
    paths:
      - $BuildDir

build-win64-cli:
  stage: build-win64-cli
  image: ghcr.io/yangjuncode/go:1.24.3
  tags:
    - docker
  extends:
    - .go-cache
  rules:
    - if: '$CI_COMMIT_REF_NAME == "main"'
      changes:
        - version.txt
  script:
    - ./scripts/build_win_cli.sh
  artifacts:
    expire_in: 7 day
    name: sip_server_client_cli_win_x64
    paths:
      - $BuildDir
