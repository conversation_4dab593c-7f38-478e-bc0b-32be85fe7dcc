package sipclient

import (
	"fmt"
	"strconv"
	"time"

	"github.com/emiago/sipgo/sip"
)

// it will keep alive until the program exits in goroutine
func (s *SipClient) RegisterAndKeepAlive() error {
	contact := s.ContactHeader()

	info := NewRegisterInfo(s.NewRequest(sip.REGISTER, "", nil), *contact, 3600*time.Second)
	s.registerInfo = info
	if err := s.Register(); err != nil {
		return err
	}
	PrintlnBeforeEnterPrompt("Register to server success!")
	go s.KeepAlive()
	return nil
}

type RegisterInfo struct {
	// Register request
	req    *sip.Request
	expiry time.Duration
}

func NewRegisterInfo(
	req *sip.Request,
	contact sip.ContactHeader,
	expiry time.Duration,
) *RegisterInfo {
	if expiry > 0 {
		expires := sip.ExpiresHeader(expiry.Seconds())
		req.AppendHeader(&expires)
	}

	req.AppendHeader(&contact)
	return &RegisterInfo{
		req:    req,
		expiry: expiry,
	}
}

func (s *SipClient) doRegister(req *sip.Request) error {
	resp, err := s.DoRequestWithAuth(req, 15*time.Second)
	if err != nil {
		return err
	}
	if resp.StatusCode != sip.StatusOK {
		return fmt.Errorf("register failed, status code: %d", resp.StatusCode)
	}

	if h := resp.GetHeader("Expires"); h != nil {
		val, err := strconv.Atoi(h.Value())
		if err != nil {
			return fmt.Errorf("Failed to parse server Expires value: %w", err)
		}
		s.registerInfo.expiry = time.Duration(val) * time.Second
	}

	if via := resp.Via(); via != nil {
		if rport, _ := via.Params.Get("rport"); rport != "" {
			// refresh contact header if rport is set
			s.registerInfo.req.ReplaceHeader(s.ContactHeader())
		}
	}
	return nil
}

func (s *SipClient) Register() error {
	return s.doRegister(s.registerInfo.req)
}

func (s *SipClient) KeepAlive() {
	t := time.NewTicker(30 * time.Second)
	defer t.Stop()
	for {
		<-t.C

		if s.Destroyed() {
			PrintlnBeforeEnterPrompt("sip client destroyed, exiting keep alive")
			return
		}
		if err := s.doRegister(s.registerInfo.req); err != nil {
			continue
		}
	}
}

func (s *SipClient) UnRegister() error {
	req := s.registerInfo.req

	req.RemoveHeader("Expires")
	req.RemoveHeader("Contact")
	req.AppendHeader(sip.NewHeader("Contact", "*"))
	expires := sip.ExpiresHeader(0)
	req.AppendHeader(&expires)

	return s.doRegister(req)
}
