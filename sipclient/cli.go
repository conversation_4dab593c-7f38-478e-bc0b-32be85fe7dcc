package sipclient

import (
	"fmt"
	"sync/atomic"
	"time"

	"github.com/emiago/sipgo/sip"
)

const (
	EnterPrompt     = "Enter command(h for help): "
	MoveCursorUp    = "\033[1A" // Move cursor up one line
	ClearLine       = "\033[2K" // Clear the current line
	BackToBeginning = "\r"      // Move cursor to the beginning of the line
)

var IsEnterPromptDisplay = atomic.Bool{} // IsEnterPromptDisplay indicates whether the enter prompt is currently displayed

func PrintHelp() {
	fmt.Print(`SIP Protocol Device Demo, it allows you to interact with SIP server as a device.
note: incoming calls will be automatically answered, and you can always hang up the call by command.
Commands:
	1. Call - Initiate a call to the specified dmrID
	2. Hangup - Hang up the current call
	3. Message - Send Message to the specified dmrID
	4. Exit - Exit the demo
`)
}

func DisplayEnterPrompt() {
	if IsEnterPromptDisplay.Load() {
		return
	}
	fmt.Print(EnterPrompt)
	IsEnterPromptDisplay.Store(true)
}

func ClearEnterPrompt() {
	if !IsEnterPromptDisplay.Load() {
		return
	}
	fmt.Print(ClearLine + BackToBeginning)
	IsEnterPromptDisplay.Store(false)
}

func PrintBeforeEnterPrompt(a ...any) {
	if !IsEnterPromptDisplay.Load() {
		fmt.Print(a...)
		return
	}

	ClearEnterPrompt()
	fmt.Print(a...)
	DisplayEnterPrompt()
}

func PrintlnBeforeEnterPrompt(i ...any) {
	if !IsEnterPromptDisplay.Load() {
		fmt.Println(i...)
		return
	}

	ClearEnterPrompt()
	fmt.Println(i...)
	DisplayEnterPrompt()
}

func PrintfBeforeEnterPrompt(msg string, args ...any) {
	if !IsEnterPromptDisplay.Load() {
		fmt.Printf(msg, args...)
		return
	}

	ClearEnterPrompt()
	fmt.Printf(msg, args...)
	DisplayEnterPrompt()
}

func DebugBeforeEnterPrompt(a ...any) {
	// TODO: use debug log level
	if !IsEnterPromptDisplay.Load() {
		fmt.Println(a...)
		return
	}

	ClearEnterPrompt()
	fmt.Println(a...)
	DisplayEnterPrompt()
}

func (s *SipClient) ProcessCall(target string) error {
	if oldDialog := s.Dialog.Load(); oldDialog != nil && oldDialog.Status.Load() < DialogStatusCallend {
		// If already in a call, respond with busy
		return fmt.Errorf("already in a call, please hang up first")
	}

	PrintlnBeforeEnterPrompt("Initiating call dialog with target: ", target)
	mediaSession, err := CreateSipMediaSession()
	if err != nil {
		return fmt.Errorf("failed to create media session: %w", err)
	}

	offer := s.GenerateSdpOffer(mediaSession.LocalPort, mediaSession.LocalSSRC)
	offerByte, err := offer.Marshal()
	if err != nil {
		return fmt.Errorf("marshal offer failed: %w", err)
	}
	mediaSession.SDP = offer

	ContentTypeHeader := sip.ContentTypeHeader(SDPContentType)
	req := s.NewRequest(sip.INVITE, target, offerByte, s.ContactHeader(), &ContentTypeHeader)
	allowHeader := sip.NewHeader("Allow", "INVITE, ACK, BYE, CANCEL, UPDATE, INFO, MESSAGE")
	req.AppendHeader(allowHeader)

	d := s.NewOutgoingDialog(req, mediaSession)
	s.Dialog.Store(d)

	PrintlnBeforeEnterPrompt("Sending INVITE request to target:", target)
	return d.InviteOutgoing(s, req)
}

func (s *SipClient) ProcessHangup() error {
	d := s.Dialog.Load()
	if d == nil {
		return fmt.Errorf("no active call to hang up")
	}

	if d.Status.Load() >= DialogStatusCallend {
		return fmt.Errorf("call already ended")
	}
	PrintlnBeforeEnterPrompt("Hanging up the call: ", d.DialogID)
	d.EndCall()
	return nil
}

func (s *SipClient) ProcessSendMessage(target, message string) error {
	req := s.NewRequest(sip.MESSAGE, target, []byte(message))

	resp, err := s.DoRequestWithAuth(req, 15*time.Second)
	if err != nil {
		return err
	}

	if resp.StatusCode != sip.StatusOK {
		return fmt.Errorf("send message failed, status code: %d,reason: %s", resp.StatusCode, resp.Reason)
	}

	return nil
}
