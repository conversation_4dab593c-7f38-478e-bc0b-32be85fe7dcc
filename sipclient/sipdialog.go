package sipclient

import (
	"context"
	"errors"
	"fmt"
	"math/rand"
	"strconv"
	"strings"
	"time"

	"sipserverclient/util"

	"github.com/emiago/sipgo"
	"github.com/emiago/sipgo/sip"
	"github.com/pion/rtp"
	"github.com/pion/sdp/v3"
	"go.uber.org/atomic"
)

const (
	DirectionIncoming = iota
	DirectionOutgoing
)

const (
	DialogStatusInviting = iota
	DialogStatusCalling
	DialogStatusCallend
)

var ErrorTemporarilyUnavailable = errors.New("temporarily unavailable")

type SipDialog struct {
	client *SipClient
	// form: callID__toTag__fromTag
	DialogID string
	// 0: incoming, 1: outgoing
	Direction      uint8
	IncomingTx     sip.ServerTransaction
	OutgoingTx     sip.ClientTransaction
	gotIncomingAck chan bool
	inviteRequest  *sip.Request
	inviteResponse *sip.Response
	MediaSession   *SipMediaSession

	startTime    time.Time
	lastDataTime atomic.Time

	pcmInput<PERSON>han  chan []int16 // channel for PCM data, cache for receive PCM data
	pcmOut<PERSON><PERSON>han chan []int16 // channel for PCM data, cache for send PCM data

	// 0: inviting
	// 1: calling
	// 2: callend
	Status atomic.Int32
}

func (s *SipClient) NewIncomingDialog(
	incomingTx sip.ServerTransaction,
	invite *sip.Request,
	mediaSession *SipMediaSession,
) *SipDialog {
	return &SipDialog{
		client:         s,
		Direction:      DirectionIncoming,
		gotIncomingAck: make(chan bool),
		IncomingTx:     incomingTx,
		inviteRequest:  invite,
		MediaSession:   mediaSession,
		startTime:      time.Now(),
		lastDataTime:   *atomic.NewTime(time.Now()),
		Status:         *atomic.NewInt32(0),
		pcmInputChan:   make(chan []int16, 17), // 1000ms / 60ms = 16.67, so 17 frames per second
	}
}

func (s *SipClient) NewOutgoingDialog(
	invite *sip.Request,
	mediaSession *SipMediaSession,
) *SipDialog {
	return &SipDialog{
		client:        s,
		Direction:     DirectionOutgoing,
		inviteRequest: invite,
		MediaSession:  mediaSession,
		startTime:     time.Now(),
		lastDataTime:  *atomic.NewTime(time.Now()),
		Status:        *atomic.NewInt32(0),
		pcmOutputChan: make(chan []int16, 17), // 1000ms / 60ms = 16.67, so 17 frames per second
	}
}

func (d *SipDialog) monitorLastDataTime() {
	ticker := time.NewTicker(time.Second)
	defer ticker.Stop()
	for {
		if d.Status.Load() != DialogStatusCalling {
			return
		}
		if <-ticker.C; true {
			if time.Since(d.lastDataTime.Load()) > 10*time.Second {
				PrintlnBeforeEnterPrompt("no data received in 10 seconds, ending call")
				d.EndCall()
			}
		}
	}
}

func (d *SipDialog) WaitInviteAnswer(ctx context.Context, tx sip.ClientTransaction) (*sip.Response, error) {
	var resp *sip.Response
	for {
		select {
		case <-tx.Done():
			return nil, fmt.Errorf("transaction died")
		case resp = <-tx.Responses():
			d.inviteResponse = resp
		case <-ctx.Done():
			d.Cancel()
			return nil, ctx.Err()
		}

		if resp != nil {
			break
		}
	}
	DebugBeforeEnterPrompt("got invite resp:", resp.String())

	d.inviteResponse = resp

	if resp.StatusCode == sip.StatusSessionInProgress {
		d.onSessionInProgress(resp)
	}

	if resp.IsProvisional() {
		return d.WaitInviteAnswer(ctx, tx)
	}

	id, err := sip.MakeDialogIDFromResponse(resp)
	if err != nil {
		PrintlnBeforeEnterPrompt("MakeDialogIDFromResponse error:", err)
		return nil, err
	}
	d.DialogID = id
	return resp, nil
}

func (d *SipDialog) onSessionInProgress(resp *sip.Response) {
	session := d.MediaSession
	err := session.SDP.Unmarshal(resp.Body())
	if err != nil {
		PrintlnBeforeEnterPrompt("Unmarshal Media183Session sdp error:", err.Error())
		return
	}

	for _, media := range session.SDP.MediaDescriptions {
		if media.MediaName.Media != "audio" {
			continue
		}

		remotePort := media.MediaName.Port.Value
		if remotePort == 0 {
			continue
		}

		codecs, err := codecsFromMediaDescription(media)
		if err != nil {
			continue
		}

		// only support one audio media
		if len(codecs) <= 0 {
			continue
		}
		session.NegotiatedCodecs = codecs
		session.RemoteSSRC, _ = SSRCFromMediaDescription(media)
		session.RemotePort = remotePort
		session.RemoteAddress = session.SDP.ConnectionInformation.Address.Address
		session.MediaCodec = &codecs[0]
		d.MediaSession.Ptime = getPtimeFromCodec(d.MediaSession.MediaCodec)
		for i := 0; i < len(codecs); i++ {
			v := codecs[i]
			if v.Name == MimeTypeDtmf && v.ClockRate == 8000 {
				session.TelephoneEventCodec = &v
				break
			}
		}
		break
	}

	if len(session.NegotiatedCodecs) <= 0 {
		println("no audio media in sdp Media183Session")
		return
	}
}

// nolint
func (d *SipDialog) InviteOutgoing(client *SipClient, req *sip.Request) error {
	d.Status.Store(DialogStatusInviting)
	defer func() {
		if d.Status.Load() != DialogStatusCalling {
			d.Status.Store(DialogStatusCallend)
		}
	}()
	d.inviteRequest = req
	DebugBeforeEnterPrompt("Send requests msg", req.Method,
		"to", req.Transport(), req.Destination(),
		"\n", req.String(),
	)
	ctx, cancel := context.WithTimeout(context.Background(), 30*time.Second)
	defer cancel()

	tx, err := d.client.Client.TransactionRequest(ctx, req, sipgo.ClientRequestBuild)
	if err != nil {
		return err
	}
	d.OutgoingTx = tx

	if err := d.MediaSession.Listen(d.MediaSession.LocalPort, ""); err != nil {
		return err
	}

	resp, err := d.WaitInviteAnswer(ctx, tx)
	if err != nil {
		return err
	}

	if resp.StatusCode == sip.StatusUnauthorized || resp.StatusCode == sip.StatusProxyAuthRequired {
		_ = d.Ack()
		tx.Terminate()

		tx, err = d.client.Client.TransactionDigestAuth(ctx, req, resp, sipgo.DigestAuth{
			Username: d.client.DmrID,
			Password: d.client.password,
		})
		DebugBeforeEnterPrompt("Send requests msg with digest auth", req.Method,
			"to", req.Transport(), req.Destination(),
			"\n", req.String(),
		)
		if err != nil {
			return err
		}
		d.OutgoingTx = tx

		resp, err = d.WaitInviteAnswer(ctx, tx)
		if err != nil {
			return err
		}
	}

	if resp.StatusCode == sip.StatusTemporarilyUnavailable {
		return errors.New(strconv.Itoa(resp.StatusCode) + " " + resp.Reason)
	}

	if resp.StatusCode != sip.StatusOK {
		_ = d.Ack()
		return fmt.Errorf("invite failed: %d %s", resp.StatusCode, resp.Reason)
	}

	err = d.parseOutgoingAnswer(resp.Body())
	if err != nil {
		d.Cancel()
		return err
	}
	err = d.MediaSession.SetRemoteAddr(d.MediaSession.RemoteAddress, d.MediaSession.RemotePort)
	if err != nil {
		d.Cancel()
		return err
	}

	if err := d.Ack(); err != nil {
		d.Cancel()
		return err
	}

	PrintlnBeforeEnterPrompt("Dialog established, dialog ID:", d.DialogID)
	d.Status.Store(DialogStatusCalling)
	d.MediaSession.isFirstRecv.Store(true)
	//go d.readPcmFromMicLoop()
	go d.monitorLastDataTime()
	go d.WriteRtpByTicker()

	return nil
}

func (d *SipDialog) parseOutgoingAnswer(data []byte) error {
	answer := &sdp.SessionDescription{}
	err := answer.Unmarshal(data)
	if err != nil {
		return err
	}

	for _, media := range answer.MediaDescriptions {
		if media.MediaName.Media != "audio" {
			continue
		}

		remotePort := media.MediaName.Port.Value
		if remotePort == 0 {
			continue
		}
		codecs, err := codecsFromMediaDescription(media)
		// parse codecs ok
		if err != nil {
			continue
		}

		if len(codecs) <= 0 {
			continue
		}

		d.MediaSession.NegotiatedCodecs = codecs
		d.MediaSession.RemoteAddress = answer.ConnectionInformation.Address.Address

		d.MediaSession.RemotePort = remotePort
		d.MediaSession.RemoteSSRC, _ = SSRCFromMediaDescription(media)
		d.MediaSession.MediaCodec = &codecs[0]
		d.MediaSession.Ptime = getPtimeFromCodec(d.MediaSession.MediaCodec)
		for i := 0; i < len(codecs); i++ {
			v := codecs[i]
			if v.Name == MimeTypeDtmf && v.ClockRate == 8000 {
				d.MediaSession.TelephoneEventCodec = &v
				break
			}
		}
	}

	if len(d.MediaSession.NegotiatedCodecs) == 0 {
		return errors.New("negotiate codec fail")
	}

	return nil
}

func (d *SipDialog) Ack() error {
	inviteRequest, inviteResponse := d.inviteRequest, d.inviteResponse
	Recipient := &inviteRequest.Recipient
	ack := sip.NewRequest(
		sip.ACK,
		*Recipient.Clone(),
	)
	ack.SipVersion = inviteRequest.SipVersion
	ack.Recipient = inviteRequest.Recipient

	maxForwardsHeader := sip.MaxForwardsHeader(70)
	ack.AppendHeader(&maxForwardsHeader)
	if h := inviteRequest.From(); h != nil {
		ack.AppendHeader(sip.HeaderClone(h))
	}

	if inviteResponse == nil {
		ack.AppendHeader(sip.HeaderClone(inviteRequest.To()))
	} else {
		if h := inviteResponse.To(); h != nil {
			ack.AppendHeader(sip.HeaderClone(h))
		}
	}

	if h := inviteRequest.CallID(); h != nil {
		ack.AppendHeader(sip.HeaderClone(h))
	}

	if h := inviteRequest.CSeq(); h != nil {
		ack.AppendHeader(sip.HeaderClone(h))
	}

	cSeq := ack.CSeq()
	cSeq.MethodName = sip.ACK

	if h := inviteRequest.Contact(); h != nil {
		ack.AppendHeader(sip.HeaderClone(h))
	}

	ack.SetTransport(inviteRequest.Transport())
	ack.SetSource(inviteRequest.Source())
	ack.SetDestination(inviteRequest.Destination())

	_, err := d.client.DoRequestWithAuth(ack, 10*time.Second)
	return err
}

// nolint
func (d *SipDialog) OnInComingInvite(client *SipClient, req *sip.Request, tx sip.ServerTransaction) error {
	d.Status.Store(DialogStatusInviting)
	defer func() {
		if d.Status.Load() != DialogStatusCalling {
			// if not calling, set status to callend due to error or timeout
			d.Status.Store(DialogStatusCallend)
		}
	}()

	// check content type header
	contentType := req.ContentType()
	if contentType == nil {
		// set default content type as application/sdp
		h := sip.ContentTypeHeader(SDPContentType)
		contentType = &h
	}
	if contentType.Value() != SDPContentType {
		resp := sip.NewResponseFromRequest(req, sip.StatusUnsupportedMediaType, "Unsupported Media Type", nil)
		resp.AppendHeader(sip.NewHeader("Accept", SDPContentType))
		_ = d.RespondIncoming(resp)
		return fmt.Errorf("Unsupported Media Type")
	}

	err := d.RespondIncoming(sip.NewResponseFromRequest(req, sip.StatusTrying, "trying", nil))
	if err != nil {
		return err
	}
	err = d.RespondIncoming(sip.NewResponseFromRequest(req, sip.StatusRinging, "Ringing", nil))
	if err != nil {
		return err
	}

	err = d.ParseInComingOffer(req.Body())
	if err != nil {
		_ = d.RespondIncoming(sip.NewResponseFromRequest(req, sip.StatusNotAcceptableHere, "sdp parse error", nil))
		return err
	}

	media := d.MediaSession
	media.LocalPort, err = util.GetLocalFreePort()
	if err != nil {
		_ = d.RespondIncoming(sip.NewResponseFromRequest(req,
			sip.StatusTemporarilyUnavailable, "no free port", nil))
		return err
	}
	media.LocalSSRC = rand.Uint32()

	answer := client.GenerateSdpAnswer(
		media.SDP,
		media.NegotiatedCodecs,
		media.LocalPort,
		media.LocalSSRC,
	)

	answerbyte, err := answer.Marshal()
	if err != nil {
		_ = d.RespondIncoming(sip.NewResponseFromRequest(req,
			sip.StatusInternalServerError, "internal error", nil))
		return err
	}

	err = media.Listen(media.LocalPort, "")
	if err != nil {
		_ = d.RespondIncoming(sip.NewResponseFromRequest(req,
			sip.StatusInternalServerError, "internal error", nil))
		return err
	}

	err = media.SetRemoteAddr(media.RemoteAddress, media.RemotePort)
	if err != nil {
		PrintlnBeforeEnterPrompt("SetRemoteAddr err", err)
	}

	resp := sip.NewResponseFromRequest(req, sip.StatusOK, "OK", answerbyte)
	resp.AppendHeader(contentType)
	err = d.RespondIncoming(resp)
	if err != nil {
		return err
	}

	ok := d.WaitAck(30 * time.Second)
	if !ok {
		return fmt.Errorf("no ack")
	}
	d.Status.Store(DialogStatusCalling)
	PrintlnBeforeEnterPrompt("Dialog established, dialog ID:", d.DialogID)
	go d.ReadRtpLoop()
	go d.keepRtpConnAliveLoop()
	//go d.writePcm2SpeakerLoop()
	go d.monitorLastDataTime()
	return nil
}

func (d *SipDialog) ParseInComingOffer(data []byte) error {
	offer := &sdp.SessionDescription{}
	err := offer.Unmarshal(data)
	if err != nil {
		return err
	}

	d.MediaSession.SDP = offer

	var negotiatedCodecs []sdp.Codec
	for _, media := range offer.MediaDescriptions {
		if media.MediaName.Media != "audio" {
			continue
		}

		remotePort := media.MediaName.Port.Value
		if remotePort == 0 {
			continue
		}

		codecs, err := codecsFromMediaDescription(media)
		if err != nil {
			continue
		}

		exactMatches := make([]sdp.Codec, 0, len(codecs))
		partialMatches := make([]sdp.Codec, 0, len(codecs))
		for _, codec := range codecs {
			matchType, err := d.matchRemoteCodec(codec, exactMatches, partialMatches)
			if err != nil {
				DebugBeforeEnterPrompt("parseInboundOffer matchRemoteCodec err",
					err)
				continue
			}

			if matchType == codecMatchExact {
				exactMatches = append(exactMatches, codec)
			} else if matchType == codecMatchPartial {
				partialMatches = append(partialMatches, codec)
			}
		}
		switch {
		case len(exactMatches) > 0:
			negotiatedCodecs = append(negotiatedCodecs, exactMatches...)
		case len(partialMatches) > 0:
			negotiatedCodecs = append(negotiatedCodecs, partialMatches...)
		default:
			// no match, not negotiated
			continue
		}

		// only support one audio media
		if len(negotiatedCodecs) <= 0 {
			continue
		}

		d.MediaSession.NegotiatedCodecs = negotiatedCodecs
		d.MediaSession.RemoteSSRC, _ = SSRCFromMediaDescription(media)
		d.MediaSession.RemotePort = remotePort
		d.MediaSession.RemoteAddress = offer.ConnectionInformation.Address.Address
		d.MediaSession.MediaCodec = &negotiatedCodecs[0]
		d.MediaSession.Ptime = getPtimeFromCodec(d.MediaSession.MediaCodec)
		for i := 0; i < len(negotiatedCodecs); i++ {
			v := negotiatedCodecs[i]
			if v.Name == MimeTypeDtmf && v.ClockRate == 8000 {
				d.MediaSession.TelephoneEventCodec = &v
				break
			}
		}
		break
	}

	// no negotiated codec
	if len(d.MediaSession.NegotiatedCodecs) == 0 {
		return errors.New("sdp negotiate fail")
	}

	return nil
}

func (d *SipDialog) matchRemoteCodec(remoteCodec sdp.Codec,
	exactMatches, partialMatches []sdp.Codec,
) (codecMatchType, error) {
	remoteFmtp := parseFMTP(remoteCodec.Fmtp)
	if apt, hasApt := remoteFmtp["apt"]; hasApt {
		payloadType, err := strconv.ParseUint(apt, 10, 8)
		if err != nil {
			return codecMatchNone, err
		}

		aptMatch := codecMatchNone
		var aptCodec sdp.Codec
		for _, codec := range exactMatches {
			if codec.PayloadType == uint8(payloadType) {
				aptMatch = codecMatchExact
				aptCodec = codec
				break
			}
		}
		if aptMatch == codecMatchNone {
			for _, codec := range partialMatches {
				if codec.PayloadType == uint8(payloadType) {
					aptMatch = codecMatchPartial
					aptCodec = codec
					break
				}
			}
		}

		if aptMatch == codecMatchNone {
			// not an error, ignore this codec that we don't support
			return codecMatchNone, nil
		}

		// replace the apt value with the original codec's payload type
		toMatchCodec := remoteCodec
		if aptMatched, mt := codecParametersSearch(aptCodec, GlobalSdpCodecs); mt == aptMatch {
			toMatchCodec.Fmtp = strings.Replace(toMatchCodec.Fmtp,
				fmt.Sprintf("apt=%d", payloadType), fmt.Sprintf("apt=%d", aptMatched.PayloadType), 1)
		}

		// if apt's media codec is partial match, then apt codec must be partial match too
		_, matchType := codecParametersSearch(toMatchCodec, GlobalSdpCodecs)
		if matchType == codecMatchExact && aptMatch == codecMatchPartial {
			matchType = codecMatchPartial
		}
		return matchType, nil
	}
	_, matchType := codecParametersSearch(remoteCodec, GlobalSdpCodecs)
	return matchType, nil
}

func (d *SipDialog) RespondIncoming(resp *sip.Response) error {
	DebugBeforeEnterPrompt("respond incoming call, rssp", resp.String())
	tx := d.IncomingTx
	if resp.Contact() == nil {
		resp.AppendHeader(d.client.ContactHeader())
	}

	d.inviteResponse = resp

	select {
	case <-tx.Done():
		// There must be some error
		return tx.Err()
	default:
	}

	// not a dialog, just respond
	if resp.IsProvisional() {
		return tx.Respond(resp)
	}

	if !resp.IsSuccess() {
		// wait ack for no 2XX
		if err := tx.Respond(resp); err != nil {
			return err
		}

		select {
		case <-tx.Acks():
			return nil
		case <-tx.Done():
			return tx.Err()
		}
	}

	id, err := sip.MakeDialogIDFromResponse(resp)
	if err != nil {
		return err
	}

	d.DialogID = id

	return tx.Respond(resp)
}

// only for 2XX.
func (d *SipDialog) GotAck(req *sip.Request) {
	id, _ := sip.MakeDialogIDFromRequest(req)
	if id == d.DialogID {
		d.gotIncomingAck <- true
	} else {
		d.gotIncomingAck <- false
	}
}

// for 2XX.
func (d *SipDialog) WaitAck(timeout time.Duration) bool {
	timer := time.NewTimer(timeout)
	select {
	case ok := <-d.gotIncomingAck:
		return ok
	case <-d.IncomingTx.Done():
		return false
	case <-timer.C:
		return false
	}
}

func (d *SipDialog) writePcm2SpeakerLoop() {
	//err := d.client.speaker.Start()
	//if err != nil {
	//	PrintlnBeforeEnterPrompt("start speaker error:", err.Error())
	//	d.EndCall()
	//	return
	//}
	//defer d.client.speaker.Stop()
	//
	//for pcm := range d.pcmInputChan {
	//	if err := d.client.speaker.Write(pcm); err != nil {
	//		DebugBeforeEnterPrompt("write pcm to speaker error:", err.Error())
	//	}
	//
	//	// check if dialog is still calling
	//	if d.Status.Load() != DialogStatusCalling {
	//		return
	//	}
	//}
}
func (d *SipDialog) keepRtpConnAliveLoop() {
	for {
		if d.Status.Load() != DialogStatusCalling {
			return
		}

		// send a dummy packet to keep the connection alive
		err := d.MediaSession.Write(make([]byte, 0))
		if err != nil {
			DebugBeforeEnterPrompt("keep rtp conn alive error:", err.Error())
			return
		}

		time.Sleep(5 * time.Second) // adjust the interval as needed
	}
}

func (d *SipDialog) ReadRtpLoop() {
	conn := d.MediaSession.conn
	for {
		if d.Status.Load() == DialogStatusCallend {
			return
		}
		buf := make([]byte, 1500)
		n, addr, err := conn.ReadFromUDP(buf)
		if err != nil {
			return
		}

		if addr != d.MediaSession.RemoteAddr() {
			_ = d.MediaSession.SetRemoteAddr(addr.IP.String(), addr.Port)
		}

		p := &rtp.Packet{}
		data := make([]byte, n)
		copy(data, buf[:n])
		if err := p.Unmarshal(data); err != nil {
			DebugBeforeEnterPrompt("read rtp loop unmarshal rtp error:", err)
			continue
		}

		err = d.OnCallingRtp(p)
		if err != nil {
			DebugBeforeEnterPrompt("deal with rtp err:", err.Error())
		}
		d.UpdateLastDataTime()
	}
}

// nolint
func (d *SipDialog) OnCallingRtp(p *rtp.Packet) error {
	media := d.MediaSession

	if media.MediaCodec == nil {
		return fmt.Errorf("media codec not init, payload type:%d", p.PayloadType)
	}

	if media.isFirstRecv.Load() {
		// first rtp packet, set remote ssrc and timestamp
		media.RemoteSSRC = p.SSRC
		media.isFirstRecv.Store(false)
		media.lastRecvTimestamp.Store(p.Timestamp - 1)
	}

	if media.RemoteSSRC != p.SSRC {
		return fmt.Errorf(
			"Remote SSRC not match, diaglog remote SSRC:%d, rtp SSRC:%d",
			d.MediaSession.RemoteSSRC,
			p.SSRC,
		)
	}

	if !isNewerTimestamp(p.Timestamp, media.lastRecvTimestamp.Load()) {
		// old packet, ignore
		DebugBeforeEnterPrompt(
			"got rtp but timestamp too old, current:",
			media.lastRecvTimestamp.Load(),
			"packet:",
			p.Timestamp,
			"payload type:",
			p.PayloadType,
		)
		return nil
	}

	d.MediaSession.lastRecvTimestamp.Store(p.Timestamp)

	var mineType string
	switch p.PayloadType {
	case media.MediaCodec.PayloadType:
		mineType = media.MediaCodec.Name
	// case media.TelephoneEventCodec.PayloadType:
	// 	mineType = media.TelephoneEventCodec.Name
	default:
		DebugBeforeEnterPrompt("unknown payload type:", p.PayloadType)
		return nil
	}

	switch mineType {
	case MimeTypePcma:
		data, err := util.G711PCMAToPCM(p.Payload)
		if err != nil {
			return err
		}

		return d.GotInputPcm(data)

	case MimeTypePcmu:
		data, err := util.G711PCMUToPCM(p.Payload)
		if err != nil {
			return err
		}
		return d.GotInputPcm(data)
	default:
		DebugBeforeEnterPrompt("dialog got unknown rtp with payload type:", p.PayloadType)
	}
	return nil
}

func (d *SipDialog) GotInputPcm(pcm []int16) error {
	d.pcmInputChan <- pcm
	return nil
}

func (d *SipDialog) GotOutputPcm(pcm []int16) {
	d.pcmOutputChan <- pcm
}

func (d *SipDialog) WriteRtpByTicker() {
	ticker := time.NewTicker(time.Duration(d.MediaSession.Ptime) * time.Millisecond)
	defer ticker.Stop()
	for {
		if d.Status.Load() != DialogStatusCalling {
			return
		}

		select {
		case pcmData := <-d.pcmOutputChan:
			// DebugBeforeEnterPrompt("WriteRtpByTicker got pcm data from channel")
			payload, err := d.MediaSession.ConvertPcmToPayload(pcmData)
			if err != nil {
				DebugBeforeEnterPrompt("convert pcm to payload error:", err.Error())
				continue
			}

			err = d.MediaSession.Write(payload)
			if err != nil {
				DebugBeforeEnterPrompt("write rtp error:", err.Error())
			}
		case <-ticker.C:
			// DebugBeforeEnterPrompt("WriteRtpByTicker got ticker")
			// no data, continue
			// DebugBeforeEnterPrompt( "WriteRtpByTicker no data, continue")
			// _ = d.MediaSession.Write(make([]byte, 0))
			d.MediaSession.nextSequenceNumber()
			d.MediaSession.nextTimestamp()
		}
	}
}

func (d *SipDialog) readPcmFromMicLoop() {
	//err := d.client.mic.Start()
	//if err != nil {
	//	PrintlnBeforeEnterPrompt("start microphone error:", err.Error())
	//	d.EndCall()
	//}
	//defer d.client.mic.Stop()
	//
	//for {
	//	if d.Status.Load() != DialogStatusCalling {
	//		return
	//	}
	//
	//	pcmData, err := d.client.mic.Read()
	//	if err != nil {
	//		DebugBeforeEnterPrompt("read pcm from mic error:", err.Error())
	//		continue
	//	}
	//
	//	d.pcmOutputChan <- pcmData
	//	d.UpdateLastDataTime()
	//}
}

func (d *SipDialog) EndCall() {
	PrintlnBeforeEnterPrompt("EndCall, dialog ID:", d.DialogID)
	defer d.Status.Store(DialogStatusCallend)

	switch d.Status.Load() {
	case DialogStatusInviting:
		if d.IncomingTx != nil {
			_ = d.IncomingTx.Respond(sip.NewResponseFromRequest(d.inviteRequest,
				sip.StatusTemporarilyUnavailable, "Busy Here", nil))
		}
		if d.OutgoingTx != nil {
			d.Cancel()
		}
	case DialogStatusCalling:
		d.Bye()
	}
}

func (d *SipDialog) newCancelRequest() *sip.Request {
	cancelReq := sip.NewRequest(sip.CANCEL, d.inviteRequest.Recipient)
	// Cancel request must match invite TOP via and only have that Via
	cancelReq.AppendHeader(sip.HeaderClone(d.inviteRequest.Via()))
	cancelReq.AppendHeader(sip.HeaderClone(d.inviteRequest.From()))
	cancelReq.AppendHeader(sip.HeaderClone(d.inviteRequest.To()))
	cancelReq.AppendHeader(sip.HeaderClone(d.inviteRequest.CallID()))
	cseqHeader := sip.HeaderClone(d.inviteRequest.CSeq()).(*sip.CSeqHeader)
	cancelReq.AppendHeader(cseqHeader)
	sip.CopyHeaders("Route", d.inviteRequest, cancelReq)
	cancelReq.SetSource(d.inviteRequest.Source())
	cancelReq.SetDestination(d.inviteRequest.Destination())
	return cancelReq
}

// only use in outgoing call.
func (d *SipDialog) Cancel() {
	if d.Direction == DirectionIncoming {
		DebugBeforeEnterPrompt("dialog", d.DialogID, "cannot cancel incoming call")
		return
	}
	cancel := d.newCancelRequest()
	resp, err := d.client.DoRequestWithAuth(cancel, 10*time.Second)
	if err != nil {
		DebugBeforeEnterPrompt("dialog", d.DialogID, "cancel error:", err)
		return
	}
	if resp.StatusCode != sip.StatusOK {
		DebugBeforeEnterPrompt("dialog", d.DialogID, "cancel failed:", resp.StatusCode, resp.Reason)
	}
}

func (d *SipDialog) Bye() {
	defer func() {
		d.Status.Store(DialogStatusCallend)
		d.MediaSession.Destroy()
	}()
	if d.Direction == DirectionOutgoing {
		err := d.ByeOutgoing()
		if err != nil {
			DebugBeforeEnterPrompt("bye outgoing call error:", err.Error())
		}
	} else {
		err := d.ByeIncoming()
		if err != nil {
			DebugBeforeEnterPrompt("bye incoming call error:", err.Error())
		}
	}
}

func (d *SipDialog) ByeOutgoing() error {
	bye := sip.NewRequest(
		sip.BYE,
		d.inviteRequest.Recipient,
	)
	bye.SipVersion = d.inviteRequest.SipVersion

	if h := d.inviteResponse.From(); h != nil {
		bye.AppendHeader(sip.HeaderClone(h))
	}
	if h := d.inviteResponse.To(); h != nil {
		bye.AppendHeader(sip.HeaderClone(h))
	}
	if h := d.inviteRequest.CallID(); h != nil {
		bye.AppendHeader(sip.HeaderClone(h))
	}
	maxForwardsHeader := sip.MaxForwardsHeader(70)
	bye.AppendHeader(&maxForwardsHeader)
	if h := d.inviteRequest.CSeq(); h != nil {
		bye.AppendHeader(sip.HeaderClone(h))
	}

	bye.SetTransport(d.inviteRequest.Transport())
	bye.SetSource(d.inviteRequest.Source())
	bye.SetDestination(d.inviteRequest.Destination())

	resp, err := d.client.DoRequestWithAuth(bye, 10*time.Second)
	if err != nil {
		return err
	}

	if resp.StatusCode != sip.StatusOK {
		return fmt.Errorf("bye failed: %d %s", resp.StatusCode, resp.Reason)
	}

	return nil
}

func (d *SipDialog) ByeIncoming() error {
	// make sure this call is established
	ctx, cancel := context.WithTimeout(context.Background(), time.Second*5)
	defer cancel()
	for {
		if d.Status.Load() < DialogStatusCalling {
			select {
			case <-d.IncomingTx.Done():
				// Wait until timeout
			case <-time.After(sip.T1):
				// Recheck state
				continue
			case <-ctx.Done():
				return ctx.Err()
			}
		}
		break
	}

	req := d.inviteRequest
	resp := d.inviteResponse

	if !resp.IsSuccess() {
		return fmt.Errorf("can not send bye on NON success response")
	}

	from := resp.From()
	to := resp.To()
	callID := resp.CallID()
	bye := sip.NewRequest(
		sip.BYE,
		from.Address,
	)
	bye.SipVersion = req.SipVersion

	newFrom := &sip.FromHeader{
		DisplayName: to.DisplayName,
		Address:     to.Address,
		Params:      to.Params,
	}

	newTo := &sip.ToHeader{
		DisplayName: from.DisplayName,
		Address:     from.Address,
		Params:      from.Params,
	}

	maxForwards := sip.MaxForwardsHeader(70)
	cSeq := &sip.CSeqHeader{
		SeqNo:      d.client.getNextSeqNo(),
		MethodName: sip.BYE,
	}

	bye.AppendHeader(newFrom)
	bye.AppendHeader(newTo)
	bye.AppendHeader(callID)
	bye.AppendHeader(&maxForwards)
	bye.AppendHeader(cSeq)

	// Check that we have still match same dialog
	byeID := sip.MakeDialogID(callID.Value(), newFrom.Params["tag"], newTo.Params["tag"])
	if d.DialogID != byeID {
		return fmt.Errorf("non matching ID %q %q", d.DialogID, byeID)
	}

	bye.SetDestination(req.Source())
	bye.SetTransport(req.Transport())

	r, err := d.client.DoRequestWithAuth(bye, 5*time.Second)
	if err != nil {
		return err
	}

	if r.StatusCode != sip.StatusOK {
		return fmt.Errorf("bye failed: %d %s", r.StatusCode, r.Reason)
	}
	return nil
}

func (d *SipDialog) OnBye(client *SipClient, req *sip.Request, tx sip.ServerTransaction) error {
	if d.Status.Load() != DialogStatusCalling {
		_ = tx.Respond(sip.NewResponseFromRequest(req,
			sip.StatusCallTransactionDoesNotExists, "call not found", nil))
		return fmt.Errorf("Call not exist")
	}

	PrintlnBeforeEnterPrompt("got BYE, dialog ID:", d.DialogID)
	var err error
	if d.Direction == DirectionIncoming {
		err = d.OnByeIncoming(req, tx)
	} else {
		err = d.OnByeOutgoing(req, tx)
	}

	d.Status.Store(DialogStatusCallend)
	d.MediaSession.Destroy()
	return err
}

func (d *SipDialog) OnByeIncoming(req *sip.Request, tx sip.ServerTransaction) error {
	id, err := sip.MakeDialogIDFromRequest(req)
	if err != nil {
		_ = tx.Respond(sip.NewResponseFromRequest(req,
			sip.StatusCallTransactionDoesNotExists, "call not found", nil))
		return fmt.Errorf("Incoming bye MakeDialogIDFromRequest error: %w", err)
	}

	if id != d.DialogID {
		_ = tx.Respond(sip.NewResponseFromRequest(req,
			sip.StatusCallTransactionDoesNotExists, "call not found", nil))
		return fmt.Errorf("Incoming bye id not match, id: %s, dialogID: %s", id, d.DialogID)
	}

	_ = tx.Respond(sip.NewResponseFromRequest(req, sip.StatusOK, "OK", nil))
	return nil
}

func (d *SipDialog) OnByeOutgoing(req *sip.Request, tx sip.ServerTransaction) error {
	id := sip.MakeDialogID(req.CallID().Value(), req.From().Params["tag"], req.To().Params["tag"])
	if id != d.DialogID {
		_ = tx.Respond(sip.NewResponseFromRequest(req,
			sip.StatusCallTransactionDoesNotExists, "call not found", nil))
		return fmt.Errorf("outgoing bye id not match, id: %s, dialogID: %s", id, d.DialogID)
	}

	_ = tx.Respond(sip.NewResponseFromRequest(req, sip.StatusOK, "OK", nil))
	return nil
}

func (d *SipDialog) UpdateLastDataTime() {
	d.lastDataTime.Store(time.Now())
}
