package sipclient

import (
	"fmt"
	"math/rand"
	"net"
	"sync"

	"sipserverclient/util"

	"go.uber.org/atomic"

	"github.com/pion/rtp"
	"github.com/pion/sdp/v3"
)

type SipMediaSession struct {
	SDP                 *sdp.SessionDescription
	NegotiatedCodecs    []sdp.Codec
	MediaCodec          *sdp.Codec
	TelephoneEventCodec *sdp.Codec
	Ptime               int
	RemoteAddress       string
	RemotePort          int
	LocalPort           int
	RemoteSSRC          uint32
	LocalSSRC           uint32
	SequenceNumber      uint16
	Timestamp           uint32

	isFirstRecv       atomic.Bool
	lastRecvTimestamp atomic.Uint32

	mu     sync.Mutex
	remote atomic.Pointer[net.UDPAddr]
	conn   *net.UDPConn

	IsDestroyed atomic.Bool
}

func CreateSipMediaSession() (session *SipMediaSession, err error) {
	port, err := util.GetLocalFreePort()
	if err != nil {
		return nil, fmt.Errorf("no free port:%w", err)
	}

	session = &SipMediaSession{
		Ptime:             20,
		LocalPort:         port,
		LocalSSRC:         rand.Uint32(),
		isFirstRecv:       *atomic.NewBool(true),
		lastRecvTimestamp: *atomic.NewUint32(0),
		mu:                sync.Mutex{},
		IsDestroyed:       atomic.Bool{},
	}
	return
}

func (m *SipMediaSession) nextSequenceNumber() uint16 {
	m.SequenceNumber++
	if m.SequenceNumber == 0 {
		m.SequenceNumber = 1
	}
	return m.SequenceNumber
}

func (m *SipMediaSession) nextTimestamp() uint32 {
	m.Timestamp += uint32(m.Ptime) * m.MediaCodec.ClockRate / 1000
	return m.Timestamp
}

func (m *SipMediaSession) Destroy() {
	if !m.IsDestroyed.CompareAndSwap(false, true) {
		return
	}

	if m.conn != nil {
		_ = m.conn.Close()
	}
}

func (m *SipMediaSession) RemoteAddr() *net.UDPAddr {
	return m.remote.Load()
}

func (m *SipMediaSession) SetRemoteAddr(host string, port int) error {
	ip := net.ParseIP(host)
	if ip == nil {
		ips, err := net.LookupIP(host)
		if err != nil {
			return fmt.Errorf("lookup ip failed: %w", err)
		}
		if len(ips) == 0 {
			return fmt.Errorf("no IP found for host: %s", host)
		}
		ip = ips[0]
	}

	m.remote.Store(&net.UDPAddr{
		IP:   ip,
		Port: port,
	})
	return nil
}

func (m *SipMediaSession) Listen(port int, ip string) error {
	if ip == "" {
		ip = "0.0.0.0"
	}

	var err error
	m.conn, err = net.ListenUDP("udp", &net.UDPAddr{
		IP:   net.ParseIP(ip),
		Port: port,
	})
	if err != nil {
		return err
	}
	return nil
}

func (m *SipMediaSession) Read() ([]byte, *net.UDPAddr, error) {
	buf := make([]byte, 1500)
	n, srcAddr, err := m.conn.ReadFromUDP(buf)
	if err != nil {
		return nil, nil, err
	}
	return buf[:n], srcAddr, nil
}

func (m *SipMediaSession) Write(data []byte) error {
	return m.WriteRTP(&rtp.Packet{
		Header: rtp.Header{
			Version:        2,
			PayloadType:    m.MediaCodec.PayloadType,
			SequenceNumber: m.nextSequenceNumber(),
			Timestamp:      m.nextTimestamp(),
			SSRC:           m.LocalSSRC,
		},
		Payload: data,
	})
}

func (m *SipMediaSession) WriteRTP(p *rtp.Packet) error {
	if m.IsDestroyed.Load() {
		return fmt.Errorf("already destroyed")
	}
	addr := m.remote.Load()
	if addr == nil {
		return fmt.Errorf("remote addr is nil")
	}
	data, err := p.Marshal()
	if err != nil {
		return err
	}
	m.mu.Lock()
	defer m.mu.Unlock()
	_, err = m.conn.WriteTo(data, addr)
	if err != nil {
		return err
	}
	return nil
}

func (m *SipMediaSession) ConvertPcmToPayload(pcmData []int16) (payload []byte, err error) {
	if len(pcmData) == 0 {
		return nil, fmt.Errorf("pcm is empty")
	}

	switch m.MediaCodec.Name {
	case MimeTypePcma:
		payload, err = util.PCMToG711PCMA(pcmData)
	case MimeTypePcmu:
		payload, err = util.PCMToG711PCMU(pcmData)
	}

	if err != nil {
		return nil, err
	}

	return
}

const u32breakpoint uint32 = 0x80000000 // half of max uint32
func isNewerTimestamp(value, previous uint32) bool {
	if value-previous == u32breakpoint {
		return value > previous
	}
	return value != previous && (value-previous) < u32breakpoint
}
