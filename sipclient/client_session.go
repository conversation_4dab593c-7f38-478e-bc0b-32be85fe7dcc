package sipclient

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"github.com/emiago/sipgo"
	"github.com/emiago/sipgo/sip"
)

func (s *SipClient) ContactHeader() *sip.ContactHeader {
	contact := &sip.ContactHeader{
		Address: sip.Uri{
			Scheme:    SIPProtocolName,
			User:      s.DmrID,
			Host:      s.localHost,
			Port:      s.localPort,
			UriParams: sip.NewParams(),
			Headers:   sip.NewParams(),
		},
	}
	return contact
}

func (s *SipClient) getNextSeqNo() uint32 {
	u := s.SeqNo.Add(1)
	if u == 0 {
		return s.SeqNo.Add(1)
	}
	return u
}

func (s *SipClient) NewRequest(
	method sip.RequestMethod,
	dstDmrID string,
	body []byte,
	headers ...sip.Header,
) *sip.Request {
	dstUri := sip.Uri{
		Scheme: SIPProtocolName,
		User:   dstDmrID,
		Host:   s.serverHost,
		Port:   s.serverPort,
	}
	req := sip.NewRequest(method, dstUri)

	via := &sip.ViaHeader{
		ProtocolName:    SIPProtocolName,
		ProtocolVersion: SIPProtocolVersion,
		Transport:       s.transport,
		Host:            s.localHost,
		Port:            s.localPort,
		Params:          sip.NewParams(),
	}
	_ = via.Params.Add("branch", sip.GenerateBranchN(9))
	req.AppendHeader(via)

	from := &sip.FromHeader{
		Address: sip.Uri{
			Scheme: SIPProtocolName,
			User:   s.DmrID,
			Host:   s.serverHost,
			Port:   s.serverPort,
		},
		Params: sip.NewParams(),
	}
	from.Params.Add("tag", sip.GenerateTagN(9))
	req.AppendHeader(from)

	to := &sip.ToHeader{
		Address: sip.Uri{
			Scheme: SIPProtocolName,
			User:   dstDmrID,
			Host:   s.serverHost,
		},
		Params: sip.NewParams(),
	}
	req.AppendHeader(to)

	cSeq := &sip.CSeqHeader{
		SeqNo:      s.getNextSeqNo(),
		MethodName: method,
	}
	req.AppendHeader(cSeq)

	maxForwards := sip.MaxForwardsHeader(70)
	req.AppendHeader(&maxForwards)

	callID := sip.RandString(10)
	callIDHeader := sip.CallIDHeader(callID)
	req.AppendHeader(&callIDHeader)

	for _, h := range headers {
		name := h.Name()
		if name == "Via" || name == "From" || name == "To" || name == "Call-ID" || name == "CSeq" ||
			name == "Max-Forwards" {
			req.ReplaceHeader(h)
			continue
		}
		req.AppendHeader(h)
	}

	req.SetBody(body)
	req.SetDestination(fmt.Sprintf("%s:%d", s.serverHost, s.serverPort))
	req.SetTransport(s.transport)
	return req
}

func (s *SipClient) DoRequestWithAuth(req *sip.Request, timeout time.Duration) (*sip.Response, error) {
	if req.IsAck() {
		err := s.Client.WriteRequest(req)
		return nil, err
	}

	ctx, cancel := context.WithTimeout(context.Background(), timeout)
	defer cancel()
	resp, err := s.Client.Do(ctx, req)
	if err != nil {
		return nil, err
	}

	if resp.StatusCode == sip.StatusUnauthorized || resp.StatusCode == sip.StatusProxyAuthRequired {
		resp, err = s.Client.DoDigestAuth(ctx, req, resp, sipgo.DigestAuth{
			Username: s.DmrID,
			Password: s.password,
		})
		if err != nil {
			return nil, fmt.Errorf("failed to authenticate request: %w", err)
		}
	}

	// set local host and port with response rport and received address
	if via := resp.Via(); via != nil {
		if rport, _ := via.Params.Get("rport"); rport != "" {
			if p, err := strconv.Atoi(rport); err == nil {
				s.localPort = p
			}
		}
		if received, _ := via.Params.Get("received"); received != "" {
			s.localHost = received
		}
	}

	return resp, nil
}

func getResponse(ctx context.Context, tx sip.ClientTransaction) (*sip.Response, error) {
	select {
	case <-tx.Done():
		return nil, fmt.Errorf("transaction died,%v", tx.Err())
	case res := <-tx.Responses():
		return res, nil
	case <-ctx.Done():
		return nil, ctx.Err()
	}
}
