package sipclient

import (
	"strconv"
	"strings"

	"github.com/pion/sdp/v3"
)

const (
	MimeTypePcmu = "PCMU"
	MimeTypePcma = "PCMA"
	MimeTypeOpus = "opus"
	MimeTypeDtmf = "telephone-event"
)

const (
	PayloadTypePcmu = 0
	PayloadTypePcma = 8
	PayloadTypeOpus = 96
	PayloadTypeDtmf = 101
)

// GlobalSdpCodecs are the default sdp codecs that gateway support
//
//	Fmtp: "maxplaybackrate=48000; sprop-maxcapturerate=8000; stereo=0; ptime=20"
var GlobalSdpCodecs = []sdp.Codec{
	// {PayloadType: PayloadTypeOpus, Name: MimeTypeOpus, ClockRate: 48000, EncodingParameters: "2"},
	{PayloadType: PayloadTypePcmu, Name: MimeTypePcmu, ClockRate: 8000},
	{PayloadType: PayloadTypePcma, Name: MimeTypePcma, ClockRate: 8000},
	// {PayloadType: PayloadTypeDtmf, Name: MimeTypeDtmf, ClockRate: 8000, Fmtp: "0-15"},
}

// static codecs that do not require a rtpmap, only audio
var StaticCodecs = map[uint8]sdp.Codec{
	0:  {PayloadType: 0, Name: "PCMU", ClockRate: 8000},
	3:  {PayloadType: 3, Name: "GSM", ClockRate: 8000},
	4:  {PayloadType: 4, Name: "G723", ClockRate: 8000},
	5:  {PayloadType: 5, Name: "DVI4", ClockRate: 8000},
	6:  {PayloadType: 6, Name: "DVI4", ClockRate: 16000},
	7:  {PayloadType: 7, Name: "LPC", ClockRate: 8000},
	8:  {PayloadType: 8, Name: "PCMA", ClockRate: 8000},
	9:  {PayloadType: 9, Name: "G722", ClockRate: 8000},
	10: {PayloadType: 10, Name: "L16", ClockRate: 441000, EncodingParameters: "2"},
	11: {PayloadType: 11, Name: "L16", ClockRate: 441000},
	12: {PayloadType: 12, Name: "QCELP", ClockRate: 8000},
	13: {PayloadType: 13, Name: "CN", ClockRate: 8000},
	14: {PayloadType: 14, Name: "MPA", ClockRate: 90000},
	15: {PayloadType: 15, Name: "G728", ClockRate: 8000},
	16: {PayloadType: 16, Name: "DVI4", ClockRate: 11025},
	17: {PayloadType: 17, Name: "DVI4", ClockRate: 22050},
	18: {PayloadType: 18, Name: "G729", ClockRate: 8000},
	33: {PayloadType: 33, Name: "MP2T", ClockRate: 90000},
}

type codecMatchType int

const (
	codecMatchNone    codecMatchType = 0
	codecMatchPartial codecMatchType = 1
	codecMatchExact   codecMatchType = 2
)

func codecParametersSearch(target sdp.Codec, codecs []sdp.Codec) (sdp.Codec, codecMatchType) {
	targetFmtp := parseFMTP(target.Fmtp)

	// First attempt to match on type + SDPFmtpLine
	for _, codec := range codecs {
		codecFmtp := parseFMTP(codec.Fmtp)
		if matchFMTP(targetFmtp, codecFmtp) &&
			strings.EqualFold(codec.Name, target.Name) &&
			codec.ClockRate == target.ClockRate {
			return codec, codecMatchExact
		}
	}
	// Fallback to just type
	for _, c := range codecs {
		if strings.EqualFold(c.Name, target.Name) {
			return c, codecMatchPartial
		}
	}

	return sdp.Codec{}, codecMatchNone
}

func codecsFromMediaDescription(m *sdp.MediaDescription) (out []sdp.Codec, err error) {
	s := &sdp.SessionDescription{
		MediaDescriptions: []*sdp.MediaDescription{m},
	}

	for _, payloadStr := range m.MediaName.Formats {
		payloadType, err := strconv.ParseUint(payloadStr, 10, 8)
		if err != nil {
			return nil, err
		}
		codec, err := s.GetCodecForPayloadType(uint8(payloadType))
		if err != nil {
			if staticCodec, ok := StaticCodecs[uint8(payloadType)]; ok {
				out = append(out, staticCodec)
				continue
			}
			return nil, err
		}

		out = append(out, codec)
	}

	return out, nil
}

func parseFMTP(data string) map[string]string {
	parameters := make(map[string]string)

	for _, p := range strings.Split(data, ";") {
		pp := strings.SplitN(strings.TrimSpace(p), "=", 2)
		key := strings.ToLower(pp[0])
		if len(key) == 0 {
			continue
		}
		var value string
		if len(pp) > 1 {
			value = pp[1]
		}
		parameters[key] = value
	}
	return parameters
}

func encodeFMTP(parameters map[string]string) (string, error) {
	out := strings.Builder{}

	for key, value := range parameters {
		if out.Len() > 0 {
			out.WriteString(";")
		}

		out.WriteString(key)

		if len(value) > 0 {
			out.WriteString("=")
			out.WriteString(value)
		}
	}
	return out.String(), nil
}

func matchFMTP(a, b map[string]string) bool {
	for k, v := range a {
		if vb, ok := b[k]; ok && !strings.EqualFold(vb, v) {
			return false
		}
	}

	for k, v := range b {
		if va, ok := b[k]; ok && !strings.EqualFold(va, v) {
			return false
		}
	}

	return true
}

func SSRCFromMediaDescription(m *sdp.MediaDescription) (uint32, bool) {
	attribute, loaded := m.Attribute("ssrc")
	if loaded {
		n, err := strconv.ParseUint(attribute, 10, 32)
		if err == nil {
			return uint32(n), true
		}
	}
	return 0, false
}

func rtpExtensionsFromMediaDescription(m *sdp.MediaDescription) (map[string]int, error) {
	out := map[string]int{}

	for _, a := range m.Attributes {
		if a.Key == sdp.AttrKeyExtMap {
			e := sdp.ExtMap{}
			if err := e.Unmarshal(a.String()); err != nil {
				return nil, err
			}

			out[e.URI.String()] = e.Value
		}
	}

	return out, nil
}

func getMidValue(media *sdp.MediaDescription) string {
	for _, attr := range media.Attributes {
		if attr.Key == "mid" {
			return attr.Value
		}
	}
	return ""
}

func setCodecParameters(codec sdp.Codec, key, value string) (sdp.Codec, error) {
	fmtp := parseFMTP(codec.Fmtp)
	fmtp[key] = value
	var err error
	codec.Fmtp, err = encodeFMTP(fmtp)
	return codec, err
}

func getPtimeFromCodec(codec *sdp.Codec) int {
	fmtp := parseFMTP(codec.Fmtp)
	t, ok := fmtp["ptime"]
	if ok {
		ptime, err := strconv.ParseUint(t, 10, 32)
		if err == nil {
			return int(ptime)
		}
	}
	return 20
}
