package sipclient

import (
	"bytes"
	"compress/zlib"
	"fmt"
	"io"
	"math/rand"
	"net"
	"sipserverclient/util"
	"strconv"
	"sync/atomic"

	"github.com/emiago/sipgo"
	"github.com/emiago/sipgo/sip"
	"github.com/pion/sdp/v3"
)

const SIPProtocolName = "sip"
const SIPProtocolVersion = "2.0"
const SDPContentType = "application/sdp"

const (
	SipClientStateIDLE   = iota
	SipClientStateInCall = iota
)

type SipClient struct {
	UserAgent *sipgo.UserAgent // User Agent Client
	Client    *sipgo.Client    // SIP Client
	Server    *sipgo.Server    // SIP Server

	transport  string // Transport protocol (e.g., "udp", "tcp")
	serverHost string // SIP server host
	serverPort int    // SIP server port
	localHost  string // Local IP address
	localPort  int    // Local port for SIP communication
	DmrID      string // SIP user name
	password   string // SIP user password

	SeqNo atomic.Uint32 // Sequence number for SIP messages

	registerInfo *RegisterInfo

	//speaker *audio.Speaker    // Audio speaker for playback
	//mic     *audio.Microphone // Audio microphone for capture

	Dialog    atomic.Pointer[SipDialog]
	destroyed atomic.Bool // Flag to indicate if the client is destroyed
}

func NewSipClient(
	transport string,
	serverHost string,
	serverPort int,
	dmrID string,
	password string,
) (*SipClient, error) {
	localIp, err := util.GetLocalIPByRemoteHost(serverHost)
	if err != nil {
		return nil, fmt.Errorf("can't get local host: %v", err)
	}
	localHost := localIp.String()
	localPort, err := util.GetLocalFreePort()
	if err != nil {
		return nil, fmt.Errorf("can't get local port: %v", err)
	}
	s := &SipClient{
		transport:  transport,
		serverHost: serverHost,
		serverPort: serverPort,
		localHost:  localHost,
		localPort:  localPort,
		DmrID:      dmrID,
		password:   password,
		SeqNo:      atomic.Uint32{},
		Dialog:     atomic.Pointer[SipDialog]{},
	}

	//UA
	s.UserAgent, err = sipgo.NewUA(
		sipgo.WithUserAgent(dmrID),
		sipgo.WithUserAgentHostname(localHost),
	)
	if err != nil {
		return nil, err
	}

	//Client
	s.Client, err = sipgo.NewClient(
		s.UserAgent,
		sipgo.WithClientNAT(),
		sipgo.WithClientHostname(localHost),
		sipgo.WithClientPort(localPort),
	)
	if err != nil {
		return nil, err
	}

	//Server
	s.Server, err = sipgo.NewServer(s.UserAgent)
	if err != nil {
		return nil, fmt.Errorf("failed to create SIP server: %v", err)
	}

	s.Server.ServeRequest(serveRequest)
	s.Server.OnInvite(s.onInvite)
	s.Server.OnAck(s.onAck)
	s.Server.OnBye(s.onBye)
	s.Server.OnMessage(s.onMessage)
	s.Server.OnOptions(s.OnOptions)

	//// init audio components
	//s.speaker, err = audio.NewSpeaker(8000, 1, 60*time.Millisecond)
	//if err != nil {
	//	return nil, fmt.Errorf("failed to create audio speaker: %v", err)
	//}
	//
	//s.mic, err = audio.NewMicrophone(8000, 1, 60*time.Millisecond)
	//if err != nil {
	//	return nil, fmt.Errorf("failed to create audio microphone: %v", err)
	//}

	return s, nil
}

func (s *SipClient) Destroyed() bool {
	return s.destroyed.Load()
}

func (s *SipClient) Close() error {
	if !s.destroyed.CompareAndSwap(false, true) {
		return nil // already destroyed
	}

	//err := s.speaker.Stop()
	//if err != nil {
	//	return fmt.Errorf("failed to stop speaker: %v", err)
	//}
	//err = s.mic.Stop()
	//if err != nil {
	//	return fmt.Errorf("failed to stop microphone: %v", err)
	//}

	_ = s.UnRegister()

	if s.Client != nil {
		if err := s.Client.Close(); err != nil {
			return err
		}
	}
	if s.Server != nil {
		if err := s.Server.Close(); err != nil {
			return err
		}
	}
	if s.UserAgent != nil {
		if err := s.UserAgent.Close(); err != nil {
			return err
		}
	}
	return nil
}

// pre process request, set rport and received
func serveRequest(req *sip.Request) {
	via := req.Via()
	if _, ok := via.Params.Get("rport"); ok {
		host, port, err := net.SplitHostPort(req.Source())
		if err != nil {
			fmt.Println("SipServerGateway.serveRequest resolve source address fail", err)
		}
		via.Params.Remove("rport")
		via.Params.Add("rport", port)
		via.Params.Add("received", host)
		req.ReplaceHeader(via)
	}

	// check content encoding
	encoding := req.GetHeader("Content-Encoding")
	if encoding == nil {
		return
	}

	if encoding.Value() == "deflate" {
		reader, err := zlib.NewReader(bytes.NewReader(req.Body()))
		if err != nil {
			return // ignore
		}
		defer reader.Close()
		var decompressed bytes.Buffer
		_, err = io.Copy(&decompressed, reader)
		if err != nil {
			return // ignore
		}
		req.SetBody(decompressed.Bytes())
	}
}

func (s *SipClient) onInvite(req *sip.Request, tx sip.ServerTransaction) {
	DebugBeforeEnterPrompt("Received INVITE request", req.String())
	if oldDialog := s.Dialog.Load(); oldDialog != nil && oldDialog.Status.Load() < DialogStatusCallend {
		// If already in a call, respond with busy
		resp := sip.NewResponseFromRequest(req, sip.StatusBusyHere, "In call, Busy Here", nil)
		if err := tx.Respond(resp); err != nil {
			PrintlnBeforeEnterPrompt(
				"Error:Failed to respond ",
				req.Recipient.String(),
				" INVITE request with 486:",
				err,
			)
		}
		return
	}

	PrintlnBeforeEnterPrompt("Received INVITE request from", req.From().Address.User)
	// Create a new dialog for the call
	mediaSession, err := CreateSipMediaSession()
	if err != nil {
		PrintlnBeforeEnterPrompt("Error: Failed to create media session:", err)
		return
	}
	dialog := s.NewIncomingDialog(tx, req, mediaSession)
	s.Dialog.Store(dialog)

	_ = dialog.OnInComingInvite(s, req, tx)
}

func (s *SipClient) onAck(req *sip.Request, tx sip.ServerTransaction) {
	DebugBeforeEnterPrompt("Received ACK request", req.String())
	d := s.Dialog.Load()
	if d != nil {
		d.GotAck(req)
	}
}

func (s *SipClient) onBye(req *sip.Request, tx sip.ServerTransaction) {
	DebugBeforeEnterPrompt("Received BYE request", req.String())
	d := s.Dialog.Load()
	if d != nil {
		err := d.OnBye(s, req, tx)
		if err != nil {
			PrintlnBeforeEnterPrompt("onBye error:", err.Error())
		}
	}
}

func (s *SipClient) onMessage(req *sip.Request, tx sip.ServerTransaction) {
	PrintlnBeforeEnterPrompt("receive messsage from", req.From().Address.User, ":", string(req.Body()))
	tx.Respond(sip.NewResponseFromRequest(req, sip.StatusOK, "OK", nil))
}

func (s *SipClient) OnOptions(req *sip.Request, tx sip.ServerTransaction) {
	resp := sip.NewResponseFromRequest(req, sip.StatusOK, "OK", nil)
	allowHeader := sip.NewHeader("Allow", "INVITE, ACK, BYE, CANCEL,MESSAGE, OPTIONS")
	resp.AppendHeader(allowHeader)
	AcceptHeader := sip.NewHeader("Accept", "application/sdp,text/plain")
	resp.AppendHeader(AcceptHeader)
	err := tx.Respond(resp)
	if err != nil {
		fmt.Println("Failed to respond to OPTIONS request:", err)
	}
}

func (s *SipClient) GenerateSdpOffer(localPort int, localSSRC uint32) *sdp.SessionDescription {
	offer := &sdp.SessionDescription{
		Version: 0,
		Origin: sdp.Origin{
			Username:       s.DmrID,
			SessionID:      rand.Uint64(),
			SessionVersion: rand.Uint64(),
			NetworkType:    "IN",
			AddressType:    "IP4",
			UnicastAddress: s.localHost,
		},
		SessionName: "Talk",
		ConnectionInformation: &sdp.ConnectionInformation{
			NetworkType: "IN",
			AddressType: "IP4",
			Address:     &sdp.Address{Address: s.localHost},
		},
		TimeDescriptions: []sdp.TimeDescription{
			{Timing: sdp.Timing{StartTime: 0, StopTime: 0}},
		},
		Attributes: make([]sdp.Attribute, 0),
	}
	media := &sdp.MediaDescription{
		MediaName: sdp.MediaName{
			Media:   "audio",
			Port:    sdp.RangedPort{Value: localPort},
			Protos:  []string{"RTP", "AVP"},
			Formats: make([]string, 0),
		},
	}
	for _, codec := range GlobalSdpCodecs {
		channels := uint16(0)
		val, err := strconv.ParseUint(codec.EncodingParameters, 10, 16)
		if err == nil {
			channels = uint16(val)
		}
		media.WithCodec(codec.PayloadType, codec.Name, codec.ClockRate, channels, codec.Fmtp)
	}

	// media.WithPropertyAttribute("rtcp-mux")
	// media.WithPropertyAttribute(sdp.AttrKeySendOnly)
	media.WithValueAttribute("ssrc", fmt.Sprintf("%d", localSSRC))
	media.WithValueAttribute("ptime", "60")

	offer.WithMedia(media)
	return offer
}

func (s *SipClient) GenerateSdpAnswer(offer *sdp.SessionDescription, negotiatedCodecs []sdp.Codec,
	localPort int, localSSRC uint32,
) *sdp.SessionDescription {
	answer := &sdp.SessionDescription{
		Version: 0,
		Origin: sdp.Origin{
			Username:       s.DmrID,
			SessionID:      offer.Origin.SessionID,
			SessionVersion: offer.Origin.SessionVersion + 2,
			NetworkType:    "IN",
			AddressType:    "IP4",
			UnicastAddress: s.localHost,
		},
		SessionName: offer.SessionName,
		ConnectionInformation: &sdp.ConnectionInformation{
			NetworkType: "IN",
			AddressType: "IP4",
			Address:     &sdp.Address{Address: s.localHost},
		},
		TimeDescriptions: []sdp.TimeDescription{
			{Timing: sdp.Timing{StartTime: 0, StopTime: 0}},
		},
	}
	media := &sdp.MediaDescription{
		MediaName: sdp.MediaName{
			Media:   "audio",
			Port:    sdp.RangedPort{Value: localPort},
			Protos:  []string{"RTP", "AVP"},
			Formats: make([]string, 0),
		},
	}

	for _, codec := range negotiatedCodecs {
		channels := uint16(0)
		val, err := strconv.ParseUint(codec.EncodingParameters, 10, 16)
		if err == nil {
			channels = uint16(val)
		}

		media.WithCodec(codec.PayloadType, codec.Name, codec.ClockRate, channels, codec.Fmtp)
	}

	// media.WithPropertyAttribute("rtcp-mux")
	// media.WithPropertyAttribute(sdp.AttrKeyRecvOnly)
	media.WithValueAttribute("ssrc", fmt.Sprintf("%d", localSSRC))
	media.WithValueAttribute("ptime", "60")

	answer.WithMedia(media)
	return answer
}
